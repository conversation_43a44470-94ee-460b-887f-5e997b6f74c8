<?php

declare(strict_types=1);

namespace App\Http\Controllers\Api\V1;

use App\Http\Requests\ReportFilterRequest;
use App\Http\Requests\ReportExportRequest;
use App\Services\ReportService;
use Illuminate\Http\JsonResponse;

/**
 * Report Controller
 *
 * Handles data reporting endpoints including sales, volume, refunds, and order status reports.
 * Implements role-based access control based on the existing four-tier permission system.
 */
final class ReportController extends ApiController
{
    public function __construct(
        private readonly ReportService $reportService
    ) {
        // Apply authorization policy for all report methods
        $this->middleware('auth:sanctum');
    }

    /**
     * Get sales report data
     *
     * Returns sales amount statistics grouped by time period and region
     */
    public function sales(ReportFilterRequest $request): JsonResponse
    {
        $this->authorize('viewSales', 'App\Policies\ReportPolicy');

        $data = $this->reportService->getSalesReport($request->getProcessedData());

        return $this->successResponse(
            $data,
            'api.reports.sales_retrieved'
        );
    }

    /**
     * Get volume report data
     *
     * Returns sales volume statistics grouped by time period and region
     */
    public function volume(ReportFilterRequest $request): JsonResponse
    {
        $this->authorize('viewVolume', 'App\Policies\ReportPolicy');

        $data = $this->reportService->getVolumeReport($request->getProcessedData());

        return $this->successResponse(
            $data,
            'api.reports.volume_retrieved'
        );
    }

    /**
     * Get refund analysis report
     *
     * Returns refund statistics and analysis data
     */
    public function refunds(ReportFilterRequest $request): JsonResponse
    {
        $this->authorize('viewRefunds', 'App\Policies\ReportPolicy');

        $data = $this->reportService->getRefundReport($request->getProcessedData());

        return $this->successResponse(
            $data,
            'api.reports.refunds_retrieved'
        );
    }

    /**
     * Get order status report
     *
     * Returns order status distribution and statistics
     */
    public function orderStatus(ReportFilterRequest $request): JsonResponse
    {
        $this->authorize('viewOrderStatus', 'App\Policies\ReportPolicy');

        $data = $this->reportService->getOrderStatusReport($request->getProcessedData());

        return $this->successResponse(
            $data,
            'api.reports.order_status_retrieved'
        );
    }

    /**
     * Export report data
     *
     * Generates and returns downloadable report files
     */
    public function export(ReportExportRequest $request): JsonResponse
    {
        $this->authorize('export', 'App\Policies\ReportPolicy');

        $result = $this->reportService->exportReport($request->validated());

        return $this->successResponse(
            $result,
            'api.reports.export_started'
        );
    }
}
